version: '3.8'

services:
  # ==========================================
  # Spark Memory MCP Server
  # ==========================================
  spark-mcp:
    build: .
    container_name: spark-mcp-server
    ports:
      - "8050:8050"
    environment:
      # MCP Server Configuration
      TRANSPORT: sse
      HOST: 0.0.0.0
      PORT: 8050
      
      # LLM Configuration (production settings from .env)
      LLM_PROVIDER: openrouter
      LLM_API_KEY: 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
      LLM_CHOICE: google/gemini-2.5-flash-lite
      LLM_BASE_URL: https://openrouter.ai/api/v1
      
      # BGE Embedding Server (external service on host)
      BGE_SERVER_URL: http://************:8080
      
      # External Supabase Database
      # Direct PostgreSQL connection required for RealSupabaseMCP
      DATABASE_URL: **************************************************************************/postgres
      
      # Legacy compatibility (unused in new architecture)
      EMBEDDING_MODEL_CHOICE: text-embedding-3-small
      
      # Logging configuration to suppress health check 404s
      UVICORN_LOG_LEVEL: info
    extra_hosts:
      - "host.docker.internal:host-gateway"  # Ensure host networking works
    networks:
      - spark-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -f http://localhost:8050/health || exit 1"]
      interval: 10s
      timeout: 15s
      retries: 10
      start_period: 120s  # Give extra time for initialization to prevent race conditions

  # ==========================================
  # PostgreSQL with pgvector (ONLY for local development/testing)
  # ==========================================
  # NOTE: This is ONLY used when you want to test without external Supabase
  # By default, the system uses external Supabase at *************:8000 via MCP
  postgres-local:
    image: pgvector/pgvector:pg16
    container_name: spark-postgres-local
    environment:
      POSTGRES_DB: spark_memory
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: spark_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_local_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/01-init.sql:ro
    networks:
      - spark-network
    restart: unless-stopped
    profiles:
      - local-postgres  # ONLY enable for local testing without external Supabase
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d spark_memory"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    command: >
      postgres 
      -c shared_preload_libraries=vector
      -c max_connections=200
      -c shared_buffers=256MB

  # ==========================================
  # BGE Embedding Server (Mock for testing)
  # ==========================================
  bge-server:
    image: nginx:alpine
    container_name: spark-bge-mock
    ports:
      - "8080:80"
    volumes:
      - ./scripts/bge-mock:/usr/share/nginx/html:ro
    networks:
      - spark-network
    restart: unless-stopped
    profiles:
      - mock-bge  # Optional mock service for testing
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # Redis for Caching (Optional)
  # ==========================================
  redis:
    image: redis:7-alpine
    container_name: spark-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - spark-network
    restart: unless-stopped
    profiles:
      - redis  # Optional service
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # ==========================================
  # Monitoring with Prometheus (Optional)
  # ==========================================
  prometheus:
    image: prom/prometheus:latest
    container_name: spark-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - spark-network
    restart: unless-stopped
    profiles:
      - monitoring
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'

# ==========================================
# Networks
# ==========================================
networks:
  spark-network:
    driver: bridge
    name: spark-network

# ==========================================
# Volumes
# ==========================================
volumes:
  postgres_local_data:
    name: spark_postgres_local_data
  redis_data:
    name: spark_redis_data
  prometheus_data:
    name: spark_prometheus_data

# ==========================================
# Usage Examples:
# ==========================================
#
# PRODUCTION deployment (uses external Supabase at *************:8000):
# docker-compose up -d
#
# With mock BGE server for testing (no external BGE needed):
# docker-compose --profile mock-bge up -d
#
# LOCAL TESTING with local PostgreSQL (instead of external Supabase):
# docker-compose --profile local-postgres up -d
#
# With Redis caching:
# docker-compose --profile redis up -d
#
# With monitoring:
# docker-compose --profile monitoring up -d
#
# Full local development stack (no external dependencies):
# docker-compose --profile local-postgres --profile mock-bge --profile redis up -d
#
# Health check:
# docker-compose ps
# curl http://localhost:8050/health
#
# View logs:
# docker-compose logs -f spark-mcp
#
# Scale the MCP server:
# docker-compose up -d --scale spark-mcp=3
#
# IMPORTANT NOTES:
# - By default: Uses external Supabase (*************:8000) and external BGE server
# - Use --profile local-postgres ONLY for testing without external Supabase
# - Use --profile mock-bge ONLY for testing without external BGE server
#