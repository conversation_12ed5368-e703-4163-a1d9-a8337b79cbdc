"""
Enhanced Database Service with Connection Pooling and Vector Optimization

Implements production-ready database service for Spark Memory MCP with:
- Connection pooling for performance
- Vector similarity index management
- Comprehensive error handling
- Performance monitoring
"""

import asyncio
import asyncpg
import json
import logging
import os
import time
from contextlib import asynccontextmanager
from typing import List, Dict, Any, Optional, AsyncGenerator
from dataclasses import dataclass, field
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class DatabaseMetrics:
    """Database performance metrics."""
    total_queries: int = 0
    total_query_time: float = 0.0
    connection_pool_size: int = 0
    connection_pool_busy: int = 0
    avg_query_time: float = 0.0
    last_updated: Optional[datetime] = None

class EnhancedDatabaseService:
    """
    Production-ready database service with connection pooling and optimization.
    
    Features:
    - AsyncPG connection pooling for performance
    - Vector similarity index management
    - Query performance monitoring
    - Automatic connection management
    - Error handling and recovery
    """
    
    def __init__(self, 
                 database_url: Optional[str] = None,
                 min_connections: int = 5,
                 max_connections: int = 20,
                 command_timeout: int = 30):
        """
        Initialize enhanced database service.
        
        Args:
            database_url: PostgreSQL connection URL
            min_connections: Minimum pool size
            max_connections: Maximum pool size
            command_timeout: Query timeout in seconds
        """
        self.database_url = database_url or os.getenv('DATABASE_URL')
        if not self.database_url:
            raise ValueError("DATABASE_URL environment variable is required")
        
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.command_timeout = command_timeout
        self.pool: Optional[asyncpg.Pool] = None
        
        # Performance monitoring
        self.metrics = DatabaseMetrics()
        
        logger.info(f"Initialized EnhancedDatabaseService with pool size {min_connections}-{max_connections}")
    
    async def initialize(self):
        """Initialize connection pool and ensure schema is ready."""
        try:
            # Create connection pool
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=self.min_connections,
                max_size=self.max_connections,
                command_timeout=self.command_timeout,
                server_settings={
                    'application_name': 'spark_memory_mcp',
                    'timezone': 'UTC'
                }
            )
            
            # Verify connection and schema
            await self._verify_schema()
            
            # Initialize vector indexes for performance
            await self._ensure_vector_indexes()
            
            logger.info("Database service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database service: {e}")
            raise
    
    async def close(self):
        """Close connection pool and cleanup resources."""
        if self.pool:
            await self.pool.close()
            logger.info("Database connection pool closed")
    
    @asynccontextmanager
    async def get_connection(self) -> AsyncGenerator[asyncpg.Connection, None]:
        """Get a database connection from the pool."""
        if not self.pool:
            raise RuntimeError("Database service not initialized")
        
        start_time = time.time()
        async with self.pool.acquire() as connection:
            try:
                yield connection
            finally:
                # Update metrics
                query_time = time.time() - start_time
                self.metrics.total_queries += 1
                self.metrics.total_query_time += query_time
                self.metrics.avg_query_time = self.metrics.total_query_time / self.metrics.total_queries
                self.metrics.last_updated = datetime.utcnow()
    
    async def execute_sql(self, sql: str, params: List = None) -> List[Dict[str, Any]]:
        """
        Execute SQL query with performance monitoring.
        
        Args:
            sql: SQL query to execute
            params: Optional parameters for the query
            
        Returns:
            Query results as list of dictionaries
        """
        start_time = time.time()
        
        try:
            async with self.get_connection() as conn:
                # Convert parameters to proper asyncpg format
                asyncpg_params = self._convert_params(params) if params else []
                
                logger.debug(f"Executing SQL: {sql[:100]}... with {len(asyncpg_params)} params")
                
                # Execute query based on type
                if sql.strip().upper().startswith('SELECT'):
                    rows = await conn.fetch(sql, *asyncpg_params)
                    result = [dict(row) for row in rows]
                elif 'RETURNING' in sql.upper():
                    rows = await conn.fetch(sql, *asyncpg_params)
                    result = [dict(row) for row in rows]
                else:
                    await conn.execute(sql, *asyncpg_params)
                    result = []
                
                execution_time = time.time() - start_time
                logger.debug(f"SQL executed in {execution_time:.3f}s, returned {len(result)} rows")
                
                return result
                
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"SQL execution failed after {execution_time:.3f}s: {e}")
            logger.error(f"SQL: {sql}")
            logger.error(f"Params: {params}")
            raise
    
    def _convert_params(self, params: List) -> List:
        """Convert parameters to asyncpg-compatible format."""
        asyncpg_params = []
        for param in params:
            if isinstance(param, list):  # Handle embedding vectors
                # Convert to string format for vector type
                vector_str = '[' + ','.join(map(str, param)) + ']'
                asyncpg_params.append(vector_str)
            elif isinstance(param, dict):
                # Convert dict to JSON string for jsonb
                asyncpg_params.append(json.dumps(param))
            else:
                asyncpg_params.append(param)
        return asyncpg_params
    
    async def _verify_schema(self):
        """Verify that required tables and extensions exist."""
        async with self.get_connection() as conn:
            # Check for pgvector extension
            extension_check = await conn.fetchval(
                "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'vector')"
            )
            if not extension_check:
                logger.warning("pgvector extension not found - vector operations may fail")
            
            # Check for memories table
            table_check = await conn.fetchval("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_name = 'memories'
                )
            """)
            if not table_check:
                logger.warning("memories table not found - creating basic schema")
                await self._create_basic_schema(conn)
    
    async def _create_basic_schema(self, conn: asyncpg.Connection):
        """Create basic schema if it doesn't exist."""
        try:
            # Create memories table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS memories (
                    id SERIAL PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    content TEXT NOT NULL,
                    embedding vector(768),
                    metadata JSONB DEFAULT '{}',
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW()
                )
            """)
            
            # Create conversation_summaries table
            await conn.execute("""
                CREATE TABLE IF NOT EXISTS conversation_summaries (
                    user_id TEXT PRIMARY KEY,
                    summary TEXT NOT NULL,
                    updated_at TIMESTAMP DEFAULT NOW()
                )
            """)
            
            logger.info("Basic database schema created")
            
        except Exception as e:
            logger.error(f"Failed to create basic schema: {e}")
            raise
    
    async def _ensure_vector_indexes(self):
        """Ensure vector similarity indexes exist for performance."""
        try:
            async with self.get_connection() as conn:
                # Check if HNSW index exists
                index_check = await conn.fetchval("""
                    SELECT EXISTS (
                        SELECT 1 FROM pg_indexes 
                        WHERE tablename = 'memories' AND indexname = 'memories_embedding_hnsw_idx'
                    )
                """)
                
                if not index_check:
                    logger.info("Creating HNSW vector index for performance...")
                    # Create HNSW index for L2 distance (matching BGE embeddings)
                    await conn.execute("""
                        CREATE INDEX CONCURRENTLY memories_embedding_hnsw_idx 
                        ON memories USING hnsw (embedding vector_l2_ops) 
                        WITH (m = 16, ef_construction = 64)
                    """)
                    logger.info("HNSW vector index created successfully")
                else:
                    logger.debug("HNSW vector index already exists")
                
                # Create additional indexes for common queries
                await conn.execute("""
                    CREATE INDEX IF NOT EXISTS memories_user_id_idx ON memories (user_id)
                """)
                await conn.execute("""
                    CREATE INDEX IF NOT EXISTS memories_created_at_idx ON memories (created_at DESC)
                """)
                
        except Exception as e:
            logger.warning(f"Failed to create vector indexes: {e}")
            # Continue without indexes - functionality will work but be slower
    
    async def store_memory(
        self, 
        user_id: str, 
        content: str, 
        embedding: List[float], 
        metadata: Optional[Dict[str, Any]] = None
    ) -> int:
        """Store a memory with its embedding."""
        sql = """
            INSERT INTO memories (user_id, content, embedding, metadata, created_at, updated_at) 
            VALUES ($1, $2, $3::vector, $4, NOW(), NOW())
            RETURNING id
        """
        
        results = await self.execute_sql(sql, [user_id, content, embedding, metadata or {}])
        
        if results and len(results) > 0:
            memory_id = results[0].get('id')
            logger.info(f"Stored memory {memory_id} for user {user_id}")
            return memory_id
        else:
            logger.error("No ID returned from memory insert")
            raise Exception("Failed to store memory")
    
    async def similarity_search(
        self, 
        embedding: List[float], 
        user_id: str, 
        threshold: float = 0.7, 
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Perform optimized vector similarity search.
        
        Uses HNSW index for performance if available.
        """
        sql = """
            SELECT id, content, metadata, created_at, updated_at,
                   (embedding <-> $1::vector) as distance
            FROM memories 
            WHERE user_id = $2 AND (embedding <-> $1::vector) < $3
            ORDER BY embedding <-> $1::vector
            LIMIT $4
        """
        
        results = await self.execute_sql(sql, [embedding, user_id, threshold, limit])
        logger.debug(f"Found {len(results)} similar memories for user {user_id}")
        return results
    
    async def get_all_memories(
        self, 
        user_id: str, 
        limit: int = 50, 
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """Get all memories for a user with pagination."""
        sql = """
            SELECT id, content, metadata, created_at, updated_at
            FROM memories 
            WHERE user_id = $1
            ORDER BY created_at DESC
            LIMIT $2 OFFSET $3
        """
        
        results = await self.execute_sql(sql, [user_id, limit, offset])
        logger.debug(f"Retrieved {len(results)} memories for user {user_id}")
        return results
    
    async def delete_all_memories(self, user_id: str) -> int:
        """Delete all memories for a user."""
        # Get count first
        count_sql = "SELECT COUNT(*) as count FROM memories WHERE user_id = $1"
        count_results = await self.execute_sql(count_sql, [user_id])
        deleted_count = count_results[0].get('count', 0) if count_results else 0
        
        # Then delete
        delete_sql = "DELETE FROM memories WHERE user_id = $1"
        await self.execute_sql(delete_sql, [user_id])
        
        logger.info(f"Deleted {deleted_count} memories for user {user_id}")
        return deleted_count
    
    async def update_memory(
        self, 
        memory_id: int, 
        content: str, 
        embedding: List[float], 
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Update an existing memory."""
        sql = """
            UPDATE memories 
            SET content = $2, embedding = $3::vector, metadata = $4, updated_at = NOW()
            WHERE id = $1
        """
        
        await self.execute_sql(sql, [memory_id, content, embedding, metadata or {}])
        logger.debug(f"Updated memory {memory_id}")
    
    async def delete_memory(self, memory_id: int):
        """Delete a specific memory."""
        sql = "DELETE FROM memories WHERE id = $1"
        await self.execute_sql(sql, [memory_id])
        logger.debug(f"Deleted memory {memory_id}")
    
    async def store_conversation_summary(self, user_id: str, summary: str):
        """Store or update conversation summary."""
        sql = """
            INSERT INTO conversation_summaries (user_id, summary, updated_at)
            VALUES ($1, $2, NOW())
            ON CONFLICT (user_id) 
            DO UPDATE SET summary = $2, updated_at = NOW()
        """
        
        await self.execute_sql(sql, [user_id, summary])
        logger.debug(f"Stored conversation summary for user {user_id}")
    
    async def get_conversation_summary(self, user_id: str) -> Optional[str]:
        """Get conversation summary for a user."""
        sql = "SELECT summary FROM conversation_summaries WHERE user_id = $1"
        results = await self.execute_sql(sql, [user_id])
        
        if results and len(results) > 0:
            return results[0].get('summary')
        return None
    
    async def get_recent_memories(self, user_id: str, limit: int = 10) -> List[str]:
        """Get recent memory contents for context."""
        sql = """
            SELECT content FROM memories 
            WHERE user_id = $1
            ORDER BY created_at DESC
            LIMIT $2
        """
        
        results = await self.execute_sql(sql, [user_id, limit])
        return [row.get('content', '') for row in results]
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get database performance metrics."""
        pool_metrics = {}
        if self.pool:
            pool_metrics = {
                'pool_size': self.pool.get_size(),
                'pool_min_size': self.pool.get_min_size(),
                'pool_max_size': self.pool.get_max_size(),
                'pool_idle_connections': self.pool.get_idle_size(),
            }
        
        return {
            'total_queries': self.metrics.total_queries,
            'avg_query_time_ms': round(self.metrics.avg_query_time * 1000, 2),
            'total_query_time_s': round(self.metrics.total_query_time, 2),
            'last_updated': self.metrics.last_updated.isoformat() if self.metrics.last_updated else None,
            **pool_metrics
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check and return status."""
        try:
            start_time = time.time()
            
            # Test basic connectivity
            async with self.get_connection() as conn:
                result = await conn.fetchval("SELECT 1")
                
            response_time = time.time() - start_time
            
            return {
                'status': 'healthy',
                'response_time_ms': round(response_time * 1000, 2),
                'pool_status': 'connected' if self.pool else 'disconnected',
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'pool_status': 'error',
                'timestamp': datetime.utcnow().isoformat()
            }