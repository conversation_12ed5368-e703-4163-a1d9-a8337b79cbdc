"""
Memory Database with PostgreSQL + pgvector integration

Implements vector similarity search with L2 distance for BGE embeddings.
Uses enhanced database service with connection pooling for production performance.
"""

import os
import json
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime
from enhanced_database_service import EnhancedDatabaseService

logger = logging.getLogger(__name__)

class MemoryDatabase:
    """
    Production-ready database with pgvector for semantic memory storage.
    
    Optimized for BGE embeddings (768 dimensions) with L2 distance similarity search.
    Uses enhanced database service with connection pooling and vector indexes.
    """
    
    def __init__(self, supabase_mcp=None, database_url: Optional[str] = None):
        """
        Initialize database with enhanced service.
        
        Args:
            supabase_mcp: Legacy MCP context (deprecated, use database_url)
            database_url: PostgreSQL connection URL for enhanced service
        """
        # Initialize enhanced database service
        self.db_service = EnhancedDatabaseService(database_url=database_url)
        
        # Keep legacy MCP for backward compatibility
        self.supabase_mcp = supabase_mcp
        
        logger.info("Initialized MemoryDatabase with EnhancedDatabaseService")
    
    async def initialize(self):
        """
        Initialize database with enhanced service, schema, and indexes.
        """
        try:
            await self.db_service.initialize()
            logger.info("Database initialized successfully with enhanced service")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def _execute_sql(self, sql: str, params: List = None):
        """
        Execute SQL using enhanced database service.
        
        Args:
            sql: SQL query to execute
            params: Optional parameters for the query
            
        Returns:
            Query results
        """
        return await self.db_service.execute_sql(sql, params)
    
    async def store_memory(
        self, 
        user_id: str, 
        content: str, 
        embedding: List[float], 
        metadata: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        Store a memory with its embedding using enhanced database service.
        
        Args:
            user_id: User identifier
            content: Memory content text
            embedding: BGE embedding vector (768 dimensions)
            metadata: Optional metadata dictionary
            
        Returns:
            ID of the stored memory
        """
        return await self.db_service.store_memory(user_id, content, embedding, metadata)
    
    async def similarity_search(
        self, 
        embedding: List[float], 
        user_id: str, 
        threshold: float = 0.7, 
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Perform optimized vector similarity search using HNSW index.
        
        Args:
            embedding: Query embedding vector
            user_id: User identifier for filtering
            threshold: Distance threshold for similarity
            limit: Maximum number of results
            
        Returns:
            List of similar memories with distance scores
        """
        return await self.db_service.similarity_search(embedding, user_id, threshold, limit)
    
    async def get_all_memories(self, user_id: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get all memories for a user with optimized pagination.
        
        Args:
            user_id: User identifier
            limit: Maximum number of memories to return
            offset: Number of memories to skip
            
        Returns:
            List of all user memories
        """
        return await self.db_service.get_all_memories(user_id, limit, offset)
    
    async def delete_all_memories(self, user_id: str) -> int:
        """
        Delete all memories for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Number of deleted memories
        """
        return await self.db_service.delete_all_memories(user_id)
    
    async def update_memory(
        self, 
        memory_id: int, 
        content: str, 
        embedding: List[float], 
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        Update an existing memory.
        
        Args:
            memory_id: Memory ID to update
            content: New content
            embedding: New embedding
            metadata: New metadata
        """
        await self.db_service.update_memory(memory_id, content, embedding, metadata)
    
    async def delete_memory(self, memory_id: int):
        """
        Delete a specific memory.
        
        Args:
            memory_id: Memory ID to delete
        """
        await self.db_service.delete_memory(memory_id)
    
    async def store_conversation_summary(self, user_id: str, summary: str):
        """
        Store or update conversation summary for a user.
        
        Args:
            user_id: User identifier
            summary: Conversation summary text
        """
        await self.db_service.store_conversation_summary(user_id, summary)
    
    async def get_conversation_summary(self, user_id: str) -> Optional[str]:
        """
        Get conversation summary for a user.
        
        Args:
            user_id: User identifier
            
        Returns:
            Conversation summary or None if not found
        """
        return await self.db_service.get_conversation_summary(user_id)
    
    async def get_recent_memories(self, user_id: str, limit: int = 10) -> List[str]:
        """
        Get recent memory contents for context.
        
        Args:
            user_id: User identifier
            limit: Number of recent memories to retrieve
            
        Returns:
            List of recent memory content strings
        """
        return await self.db_service.get_recent_memories(user_id, limit)
    
    async def close(self):
        """Close database connections and cleanup resources."""
        await self.db_service.close()
        logger.info("Database connections closed")
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get database performance metrics."""
        return await self.db_service.get_metrics()
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform database health check."""
        return await self.db_service.health_check()